#!/usr/bin/env python3
"""
Polygon MATIC Hyper-Short-Term Signal Bot
Main entry point for the AI-powered trading signal bot
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, Any

from src.signal_engine.signal_generator import SignalGenerator
from src.paper_trading.portfolio_manager import PortfolioManager
from src.logging_system.trade_logger import TradeLogger
from config.settings import Config


class SignalBot:
    """Main Signal Bot orchestrator"""
    
    def __init__(self):
        self.config = Config()
        self.signal_generator = SignalGenerator(self.config)
        self.portfolio_manager = PortfolioManager(self.config)
        self.trade_logger = TradeLogger(self.config)
        self.running = False
        
        # Set up logging
        logging.basicConfig(
            level=getattr(logging, self.config.LOG_LEVEL),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/signal_bot.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    async def scan_assets(self) -> Dict[str, Any]:
        """Scan and filter assets for trading opportunities"""
        try:
            return await self.signal_generator.scan_assets()
        except Exception as e:
            self.logger.error(f"Error scanning assets: {e}")
            return {}
    
    async def generate_signals(self, assets: Dict[str, Any]) -> list:
        """Generate trading signals from filtered assets"""
        try:
            return await self.signal_generator.generate_signals(assets)
        except Exception as e:
            self.logger.error(f"Error generating signals: {e}")
            return []
    
    async def execute_signals(self, signals: list):
        """Execute paper trades based on signals"""
        for signal in signals:
            try:
                trade_result = await self.portfolio_manager.execute_trade(signal)
                await self.trade_logger.log_trade(signal, trade_result)
            except Exception as e:
                self.logger.error(f"Error executing signal {signal}: {e}")
    
    async def run_cycle(self):
        """Run one complete bot cycle"""
        self.logger.info("Starting bot cycle")
        
        # Scan assets
        assets = await self.scan_assets()
        if not assets:
            self.logger.info("No assets found for analysis")
            return
        
        # Generate signals
        signals = await self.generate_signals(assets)
        if not signals:
            self.logger.info("No signals generated")
            return
        
        # Execute signals
        await self.execute_signals(signals)
        
        self.logger.info(f"Cycle completed - processed {len(signals)} signals")
    
    async def start(self):
        """Start the bot main loop"""
        self.logger.info("Starting Polygon MATIC Signal Bot")
        self.running = True
        
        while self.running:
            try:
                await self.run_cycle()
                
                # Sleep for configured interval (default 15 seconds)
                await asyncio.sleep(self.config.SCAN_INTERVAL)
                
            except KeyboardInterrupt:
                self.logger.info("Received shutdown signal")
                self.running = False
            except Exception as e:
                self.logger.error(f"Unexpected error in main loop: {e}")
                await asyncio.sleep(5)  # Brief pause before retry
    
    def stop(self):
        """Stop the bot"""
        self.logger.info("Stopping Signal Bot")
        self.running = False


async def main():
    """Main entry point"""
    bot = SignalBot()
    
    try:
        await bot.start()
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    finally:
        bot.stop()
        print("Signal Bot stopped")


if __name__ == "__main__":
    asyncio.run(main())
